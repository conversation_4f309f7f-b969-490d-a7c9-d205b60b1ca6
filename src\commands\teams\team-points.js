const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('team-points')
        .setDescription('Show your team\'s total points and level')
        .addStringOption(option =>
            option.setName('team_name')
                .setDescription('Check a specific team\'s points (optional)')
                .setRequired(false)
                .setAutocomplete(true)),

    async autocomplete(interaction) {
        try {
            const focusedValue = interaction.options.getFocused();
            const teamManager = getTeamManager();
            const teams = teamManager.getAllTeams();
            
            const filtered = teams.filter(team => 
                team.name.toLowerCase().includes(focusedValue.toLowerCase())
            ).slice(0, 25); // Discord limits to 25 choices

            await interaction.respond(
                filtered.map(team => ({
                    name: `${team.name} (${team.total_points} points, Level ${team.level})`,
                    value: team.name
                }))
            );
        } catch (error) {
            console.error('Error in team-points autocomplete:', error);
            await interaction.respond([]);
        }
    },

    async execute(interaction) {
        try {
            // Defer the reply immediately to prevent timeout
            await interaction.deferReply();

            const teamManager = getTeamManager();
            const specifiedTeam = interaction.options.getString('team_name');
            
            let teamName;
            let memberInfo = null;

            if (specifiedTeam) {
                teamName = specifiedTeam;
            } else {
                // Get user's team
                memberInfo = await teamManager.getMemberInfo(interaction.user.id);
                if (!memberInfo.team_id) {
                    const errorEmbed = new EmbedBuilder()
                        .setColor('#747595')
                        .setTitle('❌ Not in a Team')
                        .setDescription('You are not currently in any team. Specify a team name to check, or join a team first!')
                        .addFields({
                            name: '💡 How to Join a Team',
                            value: 'Use the team request system to join a team and start earning points!',
                            inline: false
                        })
                        .setTimestamp();

                    return await interaction.editReply({ embeds: [errorEmbed] });
                }
                teamName = memberInfo.team_name;
            }

            const result = await teamManager.getTeamInfo(teamName);

            if (!result.success) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#747595')
                    .setTitle('❌ Team Not Found')
                    .setDescription(`Team "${teamName}" was not found.`)
                    .setTimestamp();

                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            const team = result.team;
            const topMembers = team.members.slice(0, 5); // Show top 5 members

            // Calculate team level progress
            const currentLevelPoints = team.level * 500;
            const nextLevelPoints = (team.level + 1) * 500;
            const progressPoints = Math.max(0, team.total_points - currentLevelPoints);
            const pointsNeeded = Math.max(0, nextLevelPoints - team.total_points);

            // Create progress bar
            const progressPercentage = Math.min(Math.max(0, (progressPoints / 500) * 100), 100);
            const progressBarLength = 20;
            const filledBars = Math.round((progressPercentage / 100) * progressBarLength);
            const emptyBars = progressBarLength - filledBars;
            const progressBar = '█'.repeat(filledBars) + '░'.repeat(emptyBars);

            const embed = new EmbedBuilder()
                .setColor('#747595')
                .setTitle(`🏅 Team: ${team.name}`)
                .addFields(
                    { name: '🏆 Total Points', value: `${team.total_points}`, inline: true },
                    { name: '⭐ Team Level', value: `${team.level}`, inline: true },
                    { name: '👥 Members', value: `${team.memberCount}`, inline: true },
                    { 
                        name: '📊 Level Progress', 
                        value: `${progressBar}\n${progressPoints}/500 points (${pointsNeeded} needed for next level)`, 
                        inline: false 
                    },
                    { name: '📅 Created', value: `<t:${team.created_at}:F>`, inline: true },
                    { name: '📊 Average Points', value: team.memberCount > 0 ? `${Math.round(team.total_points / team.memberCount)}` : '0', inline: true }
                )
                .setTimestamp();

            // Add top members if any exist
            if (topMembers.length > 0) {
                const memberList = topMembers.map((member, index) => 
                    `${index + 1}. <@${member.id}> - ${member.points} points (Level ${member.level})`
                ).join('\n');
                
                embed.addFields({ 
                    name: '🏆 Top Members', 
                    value: memberList, 
                    inline: false 
                });
            }

            // Add user's position in team if they're a member
            if (!specifiedTeam && memberInfo) {
                const userPosition = team.members.findIndex(member => member.id === interaction.user.id) + 1;
                if (userPosition > 0) {
                    embed.addFields({
                        name: '📍 Your Position',
                        value: `#${userPosition} in the team`,
                        inline: true
                    });
                }
            }

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in team-points command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#747595')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while retrieving team information. Please try again later.')
                .setTimestamp();

            try {
                if (interaction.deferred) {
                    await interaction.editReply({ embeds: [errorEmbed] });
                } else if (interaction.replied) {
                    await interaction.followUp({
                        embeds: [errorEmbed],
                        flags: [4096] // MessageFlags.Ephemeral
                    });
                } else {
                    await interaction.reply({
                        embeds: [errorEmbed],
                        flags: [4096] // MessageFlags.Ephemeral
                    });
                }
            } catch (replyError) {
                console.error('Failed to send error message for team-points command:', replyError);
            }
        }
    }
};
