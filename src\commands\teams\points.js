const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('points')
        .setDescription('Show your points and level information')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('Check another user\'s points (optional)')
                .setRequired(false)),

    async execute(interaction) {
        try {
            const targetUser = interaction.options.getUser('user') || interaction.user;
            const isOwnProfile = targetUser.id === interaction.user.id;

            // Check if target is a bot
            if (targetUser.bot) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#747595')
                    .setTitle('❌ Invalid Target')
                    .setDescription('Bots are not tracked in the team system.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const teamManager = getTeamManager();
            const memberInfo = await teamManager.getMemberInfo(targetUser.id);

            // Calculate progress to next level
            const currentLevelPoints = memberInfo.level * 100;
            const nextLevelPoints = (memberInfo.level + 1) * 100;
            const progressPoints = Math.max(0, memberInfo.points - currentLevelPoints);
            const pointsNeeded = Math.max(0, nextLevelPoints - memberInfo.points);

            // Create progress bar
            const progressPercentage = Math.min(Math.max(0, (progressPoints / 100) * 100), 100);
            const progressBarLength = 20;
            const filledBars = Math.round((progressPercentage / 100) * progressBarLength);
            const emptyBars = progressBarLength - filledBars;
            const progressBar = '█'.repeat(filledBars) + '░'.repeat(emptyBars);

            const embed = new EmbedBuilder()
                .setColor('#747595')
                .setTitle(`${isOwnProfile ? '🏆 إحصائياتك' : `👤 إحصائيات ${targetUser.tag}`}`)
                .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }))
                .addFields(
                    { name: '🏆 النقاط', value: `${memberInfo.points}`, inline: true },
                    { name: '⭐ المستوى', value: `${memberInfo.level}`, inline: true },
                    { name: '🏅 الفريق', value: memberInfo.team_name || 'ليس في فريق', inline: true },
                    {
                        name: '📊 تقدم المستوى',
                        value: `${progressBar}\n${progressPoints}/100 نقطة (تحتاج ${pointsNeeded} للمستوى التالي)`,
                        inline: false
                    },
                    { name: '🎤 وقت الصوت', value: `${Math.round(memberInfo.voice_time / 60)} دقيقة`, inline: true },
                    { name: '🎁 آخر مكافأة يومية', value: memberInfo.last_daily > 0 ? `<t:${memberInfo.last_daily}:R>` : 'لم يتم الحصول عليها', inline: true }
                )
                .setTimestamp();

            // Add team information if member is in a team
            if (memberInfo.team_id) {
                const teamInfo = await teamManager.getTeamInfo(memberInfo.team_name);
                if (teamInfo.success) {
                    embed.addFields(
                        { name: '🏅 Team Total Points', value: `${teamInfo.team.total_points}`, inline: true },
                        { name: '🌟 Team Level', value: `${teamInfo.team.level}`, inline: true },
                        { name: '👥 Team Members', value: `${teamInfo.team.memberCount}`, inline: true }
                    );
                }
            } else {
                embed.addFields({
                    name: '💡 Join a Team',
                    value: 'You need to be in a team to earn points! Use the team request system to join one.',
                    inline: false
                });
            }

            // Add daily reminder if it's the user's own profile
            if (isOwnProfile && memberInfo.team_id) {
                const now = Date.now();
                const lastDaily = memberInfo.last_daily * 1000;
                const timeDiff = now - lastDaily;
                const oneDay = 24 * 60 * 60 * 1000;

                if (timeDiff >= oneDay) {
                    embed.addFields({
                        name: '🎁 Daily Reward Available!',
                        value: 'Use `/daily` to claim your daily points reward!',
                        inline: false
                    });
                }
            }

            await interaction.reply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in points command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#747595')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while retrieving points information. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
