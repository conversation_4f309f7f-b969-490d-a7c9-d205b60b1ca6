const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('daily')
        .setDescription('Claim your daily points reward'),

    async execute(interaction) {
        try {
            // Defer the reply immediately to prevent timeout
            await interaction.deferReply();

            const teamManager = getTeamManager();
            const result = await teamManager.claimDailyReward(interaction.user.id);

            if (!result.success) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#747595')
                    .setTitle('❌ المكافأة اليومية غير متاحة')
                    .setDescription(result.message)
                    .setTimestamp();

                if (result.message.includes('team')) {
                    errorEmbed.addFields({
                        name: '💡 كيفية الانضمام لفريق',
                        value: 'استخدم نظام طلبات الفرق للانضمام لفريق وابدأ في كسب النقاط!',
                        inline: false
                    });
                }

                return await interaction.editReply({ embeds: [errorEmbed] });
            }

            // Get updated member info
            const memberInfo = await teamManager.getMemberInfo(interaction.user.id);

            const successEmbed = new EmbedBuilder()
                .setColor('#747595')
                .setTitle('🎁 تم الحصول على المكافأة اليومية!')
                .setDescription(`حصلت على **${result.points} نقطة** كمكافأة يومية!`)
                .addFields(
                    { name: '🏆 نقاطك', value: `${memberInfo.points}`, inline: true },
                    { name: '⭐ مستواك', value: `${memberInfo.level}`, inline: true },
                    { name: '🏅 فريقك', value: memberInfo.team_name || 'لا يوجد', inline: true },
                    { name: '⏰ المكافأة التالية', value: '<t:' + Math.floor((Date.now() + 24 * 60 * 60 * 1000) / 1000) + ':R>', inline: false }
                )
                .setFooter({ text: 'عد غداً للحصول على مكافأة أخرى!' })
                .setTimestamp();

            await interaction.editReply({ embeds: [successEmbed] });

            console.log(`✅ ${interaction.user.tag} claimed daily reward: ${result.points} points`);

        } catch (error) {
            console.error('Error in daily command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#747595')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while claiming your daily reward. Please try again later.')
                .setTimestamp();

            try {
                if (interaction.deferred) {
                    await interaction.editReply({ embeds: [errorEmbed] });
                } else if (!interaction.replied) {
                    await interaction.reply({ embeds: [errorEmbed], flags: [4096] }); // MessageFlags.Ephemeral
                }
            } catch (replyError) {
                console.error('Failed to send error reply in daily command:', replyError);
            }
        }
    }
};
